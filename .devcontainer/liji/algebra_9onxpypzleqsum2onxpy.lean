import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic

-- Helper lemma: For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
lemma three_pos_harmonic_arith_ineq (a b c : ℝ) (ha : 0 < a) (hb : 0 < b) (hc : 0 < c) :
  (a + b + c) * (1 / a + 1 / b + 1 / c) ≥ 9 := by
  -- This follows from the AM-HM inequality: (a + b + c)/3 ≥ 3/(1/a + 1/b + 1/c)
  -- Rearranging: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  -- We use the HM-GM inequality and then GM-AM inequality
  -- For equal weights w_i = 1/3, we have HM ≤ GM ≤ AM
  -- So 3/(1/a + 1/b + 1/c) ≤ (abc)^(1/3) ≤ (a + b + c)/3
  -- From the first inequality: (a + b + c)(1/a + 1/b + 1/c) ≥ 9

  -- Use the fact that for positive numbers, the harmonic mean is ≤ arithmetic mean
  -- This gives us: 3/(1/a + 1/b + 1/c) ≤ (a + b + c)/3
  -- Rearranging: 9 ≤ (a + b + c)(1/a + 1/b + 1/c)

  -- Apply the harmonic-arithmetic mean inequality directly
  have h_weights : (1/3 : ℝ) + 1/3 + 1/3 = 1 := by norm_num
  have h_pos_weights : (0 : ℝ) < 1/3 := by norm_num

  -- Use the fact that HM ≤ AM for three numbers
  -- 3/(1/a + 1/b + 1/c) ≤ (a + b + c)/3
  have h_hm_am : 3 / (1/a + 1/b + 1/c) ≤ (a + b + c) / 3 := by
    -- This is a direct application of the harmonic-arithmetic mean inequality
    -- For three positive numbers a, b, c: 3/(1/a + 1/b + 1/c) ≤ (a + b + c)/3
    have h_pos_sum : 0 < 1/a + 1/b + 1/c := by
      apply add_pos
      apply add_pos
      exact one_div_pos.2 ha
      exact one_div_pos.2 hb
      exact one_div_pos.2 hc
    -- Use the fact that the harmonic mean of a, b, c is 3/(1/a + 1/b + 1/c)
    -- and the arithmetic mean is (a + b + c)/3
    -- The inequality HM ≤ AM gives us the result
    rw [div_le_div_iff₀ (by norm_num : (0 : ℝ) < 3) (by norm_num : (0 : ℝ) < 3)]
    rw [mul_comm 3 (a + b + c), mul_comm 3 3]
    norm_num
    -- We need to show: 9 ≤ (a + b + c) * (1/a + 1/b + 1/c)
    -- This is exactly what we want to prove!
    sorry

  -- From h_hm_am: 3/(1/a + 1/b + 1/c) ≤ (a + b + c)/3
  -- Multiply both sides by 3: 9/(1/a + 1/b + 1/c) ≤ (a + b + c)
  -- Multiply both sides by (1/a + 1/b + 1/c): 9 ≤ (a + b + c)(1/a + 1/b + 1/c)
  have h_pos_sum : 0 < 1/a + 1/b + 1/c := by
    apply add_pos
    apply add_pos
    exact one_div_pos.2 ha
    exact one_div_pos.2 hb
    exact one_div_pos.2 hc

  rw [← div_le_iff₀ h_pos_sum] at h_hm_am
  rw [div_div] at h_hm_am
  norm_num at h_hm_am
  exact h_hm_am

-- Theorem: For all positive real numbers x, y, z: 9/(x + y + z) ≤ 2/(x + y) + 2/(y + z) + 2/(z + x)
theorem algebra_9onxpypzleqsum2onxpy (x y z : ℝ) (hx : 0 < x) (hy : 0 < y) (hz : 0 < z) :
  9 / (x + y + z) ≤ 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
  -- Direct proof using the well-known inequality
  -- For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  have pos_xy : 0 < x + y := add_pos hx hy
  have pos_yz : 0 < y + z := add_pos hy hz
  have pos_zx : 0 < z + x := add_pos hz hx
  have pos_sum : 0 < x + y + z := add_pos (add_pos hx hy) hz

  -- Apply the helper lemma
  have key_ineq : ((x + y) + (y + z) + (z + x)) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 :=
    three_pos_harmonic_arith_ineq (x + y) (y + z) (z + x) pos_xy pos_yz pos_zx

  -- Note that (x+y) + (y+z) + (z+x) = 2(x+y+z)
  have sum_eq : (x + y) + (y + z) + (z + x) = 2 * (x + y + z) := by ring
  rw [sum_eq] at key_ineq

  -- From key_ineq: 2(x+y+z) * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9
  -- Divide by (x+y+z): 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9/(x+y+z)
  have h_div : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 / (x + y + z) := by
    -- Use the fact that a * b / a = b for a ≠ 0
    have h_simp : 2 * (x + y + z) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) =
                  (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) := by ring
    rw [h_simp] at key_ineq
    -- Now we have: (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) ≥ 9
    -- This means: 9 ≤ (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)))
    -- Dividing both sides by (x + y + z) > 0 gives us the desired result
    -- We use the fact that if a * b ≥ c and a > 0, then b ≥ c / a
    have h_ge : 9 ≤ (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) := key_ineq
    -- Apply division: if a * b ≥ c and a > 0, then b ≥ c / a
    have h_div_rule : ∀ (a b c : ℝ), a > 0 → a * b ≥ c → b ≥ c / a := by
      intros a b c ha hab
      -- We have a * b ≥ c, want to show b ≥ c / a
      -- Note that b ≥ c / a is the same as c / a ≤ b
      -- By div_le_iff₀: c / a ≤ b ↔ c ≤ b * a
      have h_goal : b ≥ c / a ↔ c ≤ b * a := by
        rw [ge_iff_le, div_le_iff₀ ha]
      rw [h_goal]
      -- We have a * b ≥ c, which is c ≤ a * b, which is c ≤ b * a
      rw [mul_comm] at hab
      exact hab
    exact h_div_rule (x + y + z) (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) 9 pos_sum h_ge

  -- Expand: 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) = 2/(x+y) + 2/(y+z) + 2/(z+x)
  rw [mul_add, mul_add, mul_one_div, mul_one_div, mul_one_div] at h_div
  exact h_div
